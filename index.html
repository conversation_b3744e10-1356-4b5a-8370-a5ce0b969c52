<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> App</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        
        p {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin: 10px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            border-left: 4px solid #667eea;
        }
        
        .version {
            color: #888;
            font-size: 0.9em;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Tauri Blank App</h1>
        <p>欢迎使用 Tauri 2.0！这是一个空白的应用程序模板。</p>
        
        <button class="button" onclick="greet()">点击问候</button>
        <button class="button" onclick="showInfo()">显示信息</button>
        
        <div class="info" id="info" style="display: none;">
            <h3>应用信息</h3>
            <p>这是一个使用 Tauri 2.0 构建的桌面应用程序。</p>
            <p>Tauri 结合了 Rust 的性能和 Web 技术的灵活性。</p>
        </div>
        
        <div class="version">
            Tauri 2.0 | Rust + Web
        </div>
    </div>

    <script>
        function greet() {
            alert('你好！欢迎使用 Tauri 应用程序！');
        }
        
        function showInfo() {
            const info = document.getElementById('info');
            if (info.style.display === 'none') {
                info.style.display = 'block';
            } else {
                info.style.display = 'none';
            }
        }
        
        // 简单的键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'g') {
                e.preventDefault();
                greet();
            }
        });
    </script>
</body>
</html>
