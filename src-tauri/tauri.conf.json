{"$schema": "https://schema.tauri.app/config/2", "productName": "tauri-blank-app", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:1420", "beforeDevCommand": "python3 -m http.server 1420", "beforeBuildCommand": "mkdir -p dist && cp index.html dist/"}, "app": {"windows": [{"title": "<PERSON><PERSON>", "width": 800, "height": 600, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}